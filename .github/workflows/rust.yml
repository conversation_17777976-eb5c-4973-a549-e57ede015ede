name: Rust

on: [push, pull_request]

env:
  CARGO_TERM_COLOR: always

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Check formatting
        run: cargo fmt -- --check
      - name: Build
        run: cargo build --verbose
      - name: Run tests
        run: cargo test --verbose
      - name: Run examples
        working-directory: ./epserde
        run: for example in examples/*.rs ; do cargo run --example "$(basename "${example%.rs}")" ; done
      - name: Run clippy
        run: cargo clippy #-- -Dclippy::all -Dclippy::cargo
      - name: Switch to nightly toolchain
        run: rustup default nightly
      - name: Test with miri
        run: MIRIFLAGS="-Zmiri-disable-isolation" cargo test
