use epserde::deser::Deserialize;
use epserde::prelude::Flags;
use epserde::ser::Serialize;
use std::fs::File;
use std::io::BufWriter;

fn main() {
    let v = vec![0u64, 10, 20, 30, 40];

    let path = std::path::PathBuf::from("/tmp/test.vector");

    println!("Writing vector...");
    let mut writer = BufWriter::new(File::create(&path).expect("Could not create temp file"));
    unsafe { v.serialize(&mut writer) }.expect("Could not write vector");
    drop(v);

    println!("mmapping vector...");
    let v =
        unsafe { <Vec<u64>>::mmap(&path, Flags::RANDOM_ACCESS) }.expect("Could not mmap vector");
    let v2: &[u64] = v.get::<Vec<u64>>();

    println!("Vector value:");
    println!("{:?}", v2);

    // This shouldn't compile
    /*
    println!("Dropping vector...");
    drop(v);
    */
    println!("Vector value:");
    println!("{:?}", v2);
}
